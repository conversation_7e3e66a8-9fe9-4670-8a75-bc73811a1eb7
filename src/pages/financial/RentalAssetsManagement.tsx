import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  message,
  Modal,
  Form,
  Popconfirm,
  Row,
  Col,
  InputNumber,
  DatePicker,
  Image,
  Tag,
  Statistic,
  Select,
  Tabs,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  FileImageOutlined,
  FileExcelOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import type { TableRowSelection } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import {
  getRentalAssetsList,
  createRentalAssetDetail,
  updateRentalAssetDetail,
  deleteRentalAssetDetail,
  getRentalAssetDetail,
  exportRentalAssetsExcel,
} from '@/api/RentalAssetsApi';
import type { AddRentalAssetsParams, Detail, RentalAssetType } from '@/types/rentalAssets';
import { SortBy, SortOrder } from '@/types/commonFinancial';
import { SimpleImageUpload } from '@/components/ImageUpload';
import { logError, getErrorMessage, handleApiResponse } from '@/utils/errorHandler';
import { formatAmountSimple, formatNumber } from '@/utils/formatUtils';
import styles from './RentalAssetsManagement.module.css';

const { Search } = Input;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Option } = Select;

interface RentalAssetFormData extends Omit<AddRentalAssetsParams, 'createDate'> {
  createDate: dayjs.Dayjs | string;
}

const RentalAssetsManagement: React.FC = () => {
  const [assets, setAssets] = useState<Detail[]>([]);
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAsset, setEditingAsset] = useState<Detail | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<Detail[]>([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [currentType, setCurrentType] = useState<RentalAssetType | 'all'>('all');
  const [form] = Form.useForm<RentalAssetFormData>();

  // 日期范围状态 - 默认当月，禁止清空
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().startOf('month'),
    dayjs().endOf('month'),
  ]);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  // 排序状态 - 初始不排序，让后端使用默认排序
  const [sortBy, setSortBy] = useState<SortBy | undefined>(undefined);
  const [sortOrder, setSortOrder] = useState<SortOrder | undefined>(undefined);

  // 租赁资产类型配置
  const assetTypeConfig = {
    all: {
      label: '全部类型',
      color: 'blue',
      icon: <AppstoreOutlined />,
      description: '显示所有租赁资产类型',
    },
    short_term: {
      label: '短期租赁',
      color: 'orange',
      icon: <ClockCircleOutlined />,
      description: '短期租赁资产，如临时场地、设备等',
    },
    long_term: {
      label: '长期租赁',
      color: 'green',
      icon: <CalendarOutlined />,
      description: '长期租赁资产，如办公场地、仓库等',
    },
  };

  // 初始化数据
  useEffect(() => {
    const [startTime, endTime] = dateRange;
    fetchAssets(
      1,
      10,
      '',
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      currentType,
      sortBy,
      sortOrder,
    );
  }, [dateRange, currentType]);

  // 快捷键监听
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // F8: 保存
      if (event.key === 'F8' && modalVisible) {
        event.preventDefault();
        handleSave();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [modalVisible]);

  // 获取租赁资产列表
  const fetchAssets = async (
    page = 1,
    pageSize = 10,
    search = '',
    startTime = '',
    endTime = '',
    type: RentalAssetType | 'all',
    currentSortBy = sortBy,
    currentSortOrder = sortOrder,
  ) => {
    setLoading(true);
    try {
      const params: any = {
        page,
        pageSize,
        startTime,
        endTime,
        search: search.trim() || undefined,
      };

      // 只有在有排序参数时才传递
      if (currentSortBy && currentSortOrder) {
        params.sortBy = currentSortBy;
        params.sortOrder = currentSortOrder;
      }

      // 如果不是"全部类型"，则传递type参数
      if (type !== 'all') {
        params.type = type;
      }

      const response = await getRentalAssetsList(params);

      if (response.code === 200) {
        setAssets(response.data.details);
        setTotalAmount(response.data.totalAmount);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.message || '获取租赁资产列表失败');
      }
    } catch (error: any) {
      logError('获取租赁资产列表', error);
      message.error(getErrorMessage(error, '获取租赁资产列表失败'));
    } finally {
      setLoading(false);
    }
  };

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    const [startTime, endTime] = dateRange;
    fetchAssets(
      1,
      pagination.pageSize,
      value,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      currentType,
    );
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 日期范围变化处理
  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setDateRange([dates[0], dates[1]]);
      fetchAssets(
        1,
        pagination.pageSize,
        searchText,
        dates[0].format('YYYY-MM-DD'),
        dates[1].format('YYYY-MM-DD'),
        currentType,
      );
      setPagination((prev) => ({ ...prev, current: 1 }));
    }
  };

  // 类型切换处理
  const handleTypeChange = (type: string) => {
    setCurrentType(type as RentalAssetType | 'all');
    setSelectedRowKeys([]);
    setSelectedRows([]);
    const [startTime, endTime] = dateRange;
    fetchAssets(
      1,
      pagination.pageSize,
      searchText,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      type as RentalAssetType | 'all',
    );
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  // 分页和排序处理
  const handleTableChange = (paginationConfig: TablePaginationConfig, _: any, sorter: any) => {
    const { current = 1, pageSize = 10 } = paginationConfig;
    setPagination((prev) => ({ ...prev, current, pageSize }));

    // 处理排序
    let newSortBy: SortBy | undefined = sortBy;
    let newSortOrder: SortOrder | undefined = sortOrder;

    if (sorter && sorter.field) {
      // 根据排序字段映射到后端字段
      if (sorter.field === 'amount') {
        newSortBy = SortBy.AMOUNT;
      } else if (sorter.field === 'createDate') {
        newSortBy = SortBy.CREATE_DATE;
      }

      // 处理排序方向，包括取消排序的情况
      if (sorter.order === 'ascend') {
        newSortOrder = SortOrder.ASC;
      } else if (sorter.order === 'descend') {
        newSortOrder = SortOrder.DESC;
      } else {
        // 取消排序时，清空排序参数
        newSortBy = undefined;
        newSortOrder = undefined;
      }

      // 更新排序状态
      setSortBy(newSortBy);
      setSortOrder(newSortOrder);
    }

    const [startTime, endTime] = dateRange;
    fetchAssets(
      current,
      pageSize,
      searchText,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      currentType,
      newSortBy,
      newSortOrder,
    );
  };

  // 刷新列表
  const handleRefresh = () => {
    const [startTime, endTime] = dateRange;
    fetchAssets(
      pagination.current,
      pagination.pageSize,
      searchText,
      startTime.format('YYYY-MM-DD'),
      endTime.format('YYYY-MM-DD'),
      currentType,
    );
  };

  // 新增租赁资产
  const handleAdd = () => {
    setEditingAsset(null);
    form.resetFields();
    // 设置默认类型（如果当前不是"全部类型"）和默认创建日期
    const defaultValues: any = {
      createDate: dayjs(),
    };
    if (currentType !== 'all') {
      defaultValues.type = currentType;
    }
    form.setFieldsValue(defaultValues);
    setModalVisible(true);
  };

  // 编辑租赁资产
  const handleEdit = async (record: Detail) => {
    try {
      const response = await getRentalAssetDetail(record.id);
      if (response.code === 200) {
        setEditingAsset(record);
        form.setFieldsValue({
          type: response.data.type,
          amount: response.data.amount,
          screenshot: response.data.screenshot,
          remark: response.data.remark,
          createDate: response.data.createDate ? dayjs(response.data.createDate) : dayjs(),
        });
        setModalVisible(true);
      } else {
        message.error(response.message || '获取租赁资产详情失败');
      }
    } catch (error: any) {
      logError('获取租赁资产详情', error);
      message.error(getErrorMessage(error, '获取租赁资产详情失败'));
    }
  };

  // 删除租赁资产
  const handleDelete = async (id: string) => {
    try {
      const response = await deleteRentalAssetDetail(id);
      const result = handleApiResponse(response, '删除成功', '删除失败');

      if (result.success) {
        message.success(result.message);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError('删除租赁资产', error);
      message.error(getErrorMessage(error, '删除失败'));
    }
  };

  // 保存租赁资产
  const handleSave = async () => {
    if (saveLoading) return; // 防止重复提交

    setSaveLoading(true);
    try {
      const values = await form.validateFields();
      // 格式化数据
      const formattedValues = {
        ...values,
        createDate: values.createDate
          ? typeof values.createDate === 'string'
            ? values.createDate
            : values.createDate.format('YYYY-MM-DD')
          : dayjs().format('YYYY-MM-DD'),
      };
      let response;

      if (editingAsset) {
        // 编辑模式
        response = await updateRentalAssetDetail(editingAsset.id, formattedValues);
      } else {
        // 新增模式
        response = await createRentalAssetDetail(formattedValues);
      }

      const result = handleApiResponse(
        response,
        editingAsset ? '更新成功' : '创建成功',
        editingAsset ? '更新失败' : '创建失败',
      );

      if (result.success) {
        message.success(result.message);
        setModalVisible(false);
        handleRefresh();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      logError(editingAsset ? '更新租赁资产' : '创建租赁资产', error);
      message.error(getErrorMessage(error, editingAsset ? '更新失败' : '创建失败'));
    } finally {
      setSaveLoading(false);
    }
  };

  // 导出Excel（选中的记录）
  const handleExport = async () => {
    try {
      const [startTime, endTime] = dateRange;
      const params = {
        startTime: startTime.format('YYYY-MM-DD'),
        endTime: endTime.format('YYYY-MM-DD'),
        detailIds: selectedRowKeys.length > 0 ? (selectedRowKeys as string[]) : undefined,
        type: currentType !== 'all' ? currentType : undefined,
        search: searchText.trim() || undefined,
      };

      const blob = await exportRentalAssetsExcel(params);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      const typeLabel =
        currentType === 'all'
          ? '全部类型'
          : assetTypeConfig[currentType as keyof typeof assetTypeConfig]?.label || '';
      const fileName =
        selectedRowKeys.length > 0
          ? `租赁资产_选中记录_${typeLabel}_${startTime.format('YYYY-MM-DD')}_${endTime.format('YYYY-MM-DD')}.xlsx`
          : `租赁资产_${typeLabel}_${startTime.format('YYYY-MM-DD')}_${endTime.format('YYYY-MM-DD')}.xlsx`;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error: any) {
      logError('导出租赁资产Excel', error);
      message.error(getErrorMessage(error, '导出失败'));
    }
  };

  // 导出全部记录
  const handleExportAll = async () => {
    try {
      const [startTime, endTime] = dateRange;
      const params = {
        startTime: startTime.format('YYYY-MM-DD'),
        endTime: endTime.format('YYYY-MM-DD'),
        type: currentType !== 'all' ? currentType : undefined,
        search: searchText.trim() || undefined,
      };

      const blob = await exportRentalAssetsExcel(params);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      const typeLabel =
        currentType === 'all'
          ? '全部类型'
          : assetTypeConfig[currentType as keyof typeof assetTypeConfig]?.label || '';
      link.download = `租赁资产_全部记录_${typeLabel}_${startTime.format('YYYY-MM-DD')}_${endTime.format('YYYY-MM-DD')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } catch (error: any) {
      logError('导出全部租赁资产Excel', error);
      message.error(getErrorMessage(error, '导出失败'));
    }
  };

  // 表格行选择配置
  const rowSelection: TableRowSelection<Detail> = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[], newSelectedRows: Detail[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
      setSelectedRows(newSelectedRows);
    },
    getCheckboxProps: (record: Detail) => ({
      disabled: record.isDeleted,
    }),
  };

  // 获取类型标签样式
  const getTypeTagClass = (type: RentalAssetType) => {
    const typeMap = {
      short_term: 'shortTerm',
      long_term: 'longTerm',
    };
    return `${styles.typeTag} ${styles[typeMap[type]]}`;
  };

  // 表格列定义
  const columns: ColumnsType<Detail> = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: RentalAssetType) => {
        const config = assetTypeConfig[type];
        return (
          <Tag className={getTypeTagClass(type)} icon={config.icon}>
            {config.label}
          </Tag>
        );
      },
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 150,
      render: (amount: number) => <Tag color="green">{formatAmountSimple(amount)}</Tag>,
      sorter: (a, b) => a.amount - b.amount,
    },
    {
      title: '截图',
      dataIndex: 'screenshot',
      key: 'screenshot',
      width: 100,
      render: (screenshot: string) =>
        screenshot ? (
          <Image
            width={50}
            height={50}
            src={screenshot}
            style={{ objectFit: 'cover', borderRadius: 4 }}
            placeholder={<FileImageOutlined style={{ fontSize: 20, color: '#ccc' }} />}
          />
        ) : (
          <div
            style={{
              width: 50,
              height: 50,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: '#f5f5f5',
              borderRadius: 4,
            }}
          >
            <FileImageOutlined style={{ fontSize: 20, color: '#ccc' }} />
          </div>
        ),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      render: (text: string) => text || '-',
    },
    {
      title: '创建日期',
      dataIndex: 'createDate',
      key: 'createDate',
      width: 120,
      render: (text: string) => (text ? dayjs(text).format('YYYY-MM-DD') : '-'),
      sorter: (a, b) => dayjs(a.createDate).unix() - dayjs(b.createDate).unix(),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
            disabled={record.isDeleted}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条租赁资产记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
            disabled={record.isDeleted}
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
              disabled={record.isDeleted}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        {/* 统计信息 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Statistic
              title="总金额"
              value={totalAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
              formatter={(value) => formatNumber(value as number, 2)}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="记录总数"
              value={pagination.total}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="已选择"
              value={selectedRowKeys.length}
              valueStyle={{ color: '#722ed1' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="选中金额"
              value={selectedRows.reduce((sum, row) => sum + row.amount, 0)}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#fa8c16' }}
              formatter={(value) => formatNumber(value as number, 2)}
            />
          </Col>
        </Row>

        {/* 类型标签页和工具栏 */}
        <Row gutter={[12, 8]} justify="space-between" align="middle" style={{ marginBottom: 12 }}>
          <Col flex="auto">
            <Tabs
              activeKey={currentType}
              onChange={handleTypeChange}
              size="small"
              items={Object.entries(assetTypeConfig).map(([key, config]) => ({
                key,
                label: (
                  <span style={{ fontSize: '12px' }}>
                    {config.icon}
                    <span style={{ marginLeft: 4 }}>{config.label}</span>
                  </span>
                ),
              }))}
            />
          </Col>
          <Col>
            <Space size="small">
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd} size="small">
                新增
              </Button>
              <Button
                type="default"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
                size="small"
              >
                刷新
              </Button>
              <Button
                type="default"
                icon={<FileExcelOutlined />}
                onClick={handleExportAll}
                size="small"
              >
                导出
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 搜索和筛选 */}
        <Row gutter={[8, 8]} style={{ marginBottom: 12 }}>
          <Col flex="auto">
            <Search
              placeholder="搜索备注内容"
              allowClear
              enterButton={<SearchOutlined />}
              size="small"
              onSearch={handleSearch}
              style={{ width: 250 }}
            />
          </Col>
          <Col>
            <RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
              size="small"
              format="YYYY-MM-DD"
              allowClear={false}
            />
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={assets}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 租赁资产编辑模态框 */}
      <Modal
        title={
          <div>
            {editingAsset ? '编辑租赁资产' : '新增租赁资产'}
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>快捷键：F8 保存</div>
          </div>
        }
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnClose
        confirmLoading={saveLoading}
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="type"
                label="资产类型"
                rules={[{ required: true, message: '请选择资产类型' }]}
              >
                <Select placeholder="请选择资产类型" size="large">
                  {Object.entries(assetTypeConfig)
                    .filter(([key]) => key !== 'all') // 排除"全部类型"选项
                    .map(([key, config]) => (
                      <Option key={key} value={key}>
                        <Space>
                          {config.icon}
                          {config.label}
                        </Space>
                      </Option>
                    ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="amount"
                label="金额"
                rules={[
                  { required: true, message: '请输入金额' },
                  { type: 'number', min: 0, message: '金额不能小于0' },
                ]}
              >
                <InputNumber
                  placeholder="请输入金额"
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  addonBefore="¥"
                  size="large"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="createDate"
                label="创建日期"
                rules={[{ required: true, message: '请选择创建日期' }]}
              >
                <DatePicker
                  placeholder="请选择创建日期"
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                  size="large"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="screenshot"
                label="截图"
                rules={[{ required: true, message: '请上传截图' }]}
              >
                <SimpleImageUpload folder="rental-assets" enablePaste={true} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="remark" label="备注">
                <TextArea placeholder="请输入备注" rows={3} maxLength={500} showCount />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default RentalAssetsManagement;
