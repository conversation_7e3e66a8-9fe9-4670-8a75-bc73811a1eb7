/* 容器样式 */
.container {
  padding: 4px;
  background: #f0f2f5;
  min-height: 100vh;
}

/* 工具栏样式 */
.toolbar {
  margin-bottom: 4px;
}

.toolbar .ant-row {
  align-items: center;
}

.toolbar .ant-input-search {
  max-width: 300px;
}

/* 说明信息样式 */
.notice {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border: 1px solid #91d5ff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
  overflow: hidden;
}

.notice::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #1890ff;
}

.noticeTitle {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.noticeContent {
  font-size: 14px;
  color: #595959;
  line-height: 1.6;
}

.noticeContent strong {
  color: #1890ff;
  font-weight: 600;
  background: rgba(24, 144, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  margin: 0 2px;
}

/* 类型标签样式 */
.typeTag {
  font-weight: 500;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.typeTag.shortTerm {
  background: #fff2e8;
  border-color: #ffbb96;
  color: #fa8c16;
}

.typeTag.longTerm {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.typeTag.all {
  background: #f0f5ff;
  border-color: #adc6ff;
  color: #2f54eb;
}

/* 统计卡片样式 */
.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 表格样式 */
.ant-table-thead > tr > th {
  background-color: #f0f5ff;
  font-weight: 600;
  color: #262626;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.ant-table-tbody > tr.ant-table-row-selected > td {
  background-color: #e6f7ff;
}

.ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background-color: #bae7ff;
}

/* 图片预览样式 */
.ant-image {
  border-radius: 4px;
  overflow: hidden;
}

.ant-image-img {
  transition: transform 0.3s ease;
}

.ant-image:hover .ant-image-img {
  transform: scale(1.1);
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input-number,
.ant-select-selector {
  border-radius: 4px;
}

.ant-input:focus,
.ant-input-focused,
.ant-input-number:focus,
.ant-input-number-focused,
.ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.ant-btn-danger {
  color: #ff4d4f;
  border-color: #ff4d4f;
}

.ant-btn-danger:hover,
.ant-btn-danger:focus {
  color: #ff7875;
  border-color: #ff7875;
}

/* 标签样式 */
.ant-tag {
  font-weight: 500;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 日期选择器样式 */
.ant-picker {
  border-radius: 4px;
}

.ant-picker:hover,
.ant-picker-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 文本域样式 */
.ant-input {
  border-radius: 4px;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }

  .toolbar .ant-row {
    flex-direction: column;
    gap: 16px;
  }

  .toolbar .ant-input-search {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .ant-statistic-content {
    font-size: 20px;
  }

  .ant-table-scroll {
    overflow-x: auto;
  }
}
