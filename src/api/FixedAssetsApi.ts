import { request } from './api';
import {
  AddFixedAssetsParams,
  FixedAssetsListParams,
  FixedAssetsListResponse,
  ExportFixedAssetsParams,
  ApiResponse,
} from '@/types/fixedAssets';

// 创建固定资产详情 - POST /fixed-assets/details
export const createFixedAssetDetail = async (
  assetData: AddFixedAssetsParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/fixed-assets/details',
    method: 'POST',
    data: assetData,
  });
};

// 获取固定资产详情列表 - GET /fixed-assets/details
export const getFixedAssetsList = async (
  params: FixedAssetsListParams,
): Promise<ApiResponse<FixedAssetsListResponse['data']>> => {
  return request({
    url: '/fixed-assets/details',
    method: 'GET',
    params: {
      ...params,
      // 确保排序参数被正确传递
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
    },
  });
};

// 获取固定资产详情 - GET /fixed-assets/details/{id}
export const getFixedAssetDetail = async (id: string): Promise<ApiResponse<any>> => {
  return request({
    url: `/fixed-assets/details/${id}`,
    method: 'GET',
  });
};

// 更新固定资产详情 - PATCH /fixed-assets/details/{id}
export const updateFixedAssetDetail = async (
  id: string,
  assetData: AddFixedAssetsParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/fixed-assets/details/${id}`,
    method: 'PATCH',
    data: assetData,
  });
};

// 删除固定资产详情 - DELETE /fixed-assets/details/{id}
export const deleteFixedAssetDetail = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/fixed-assets/details/${id}`,
    method: 'DELETE',
  });
};

// 导出固定资产Excel - GET /fixed-assets/export/excel
export const exportFixedAssetsExcel = async (params: ExportFixedAssetsParams): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  // startTime和endTime现在是必填参数
  queryParams.append('startTime', params.startTime);
  queryParams.append('endTime', params.endTime);
  if (params.detailIds && params.detailIds.length > 0) {
    // 将UUID数组转换为逗号分隔的字符串，并进行URL编码
    const detailIdsString = params.detailIds.join(',');
    queryParams.append('detailIds', encodeURIComponent(detailIdsString));
  }
  if (params.search) {
    queryParams.append('search', encodeURIComponent(params.search));
  }
  // 添加排序参数
  if (params.sortBy) {
    queryParams.append('sortBy', params.sortBy);
  }
  if (params.sortOrder) {
    queryParams.append('sortOrder', params.sortOrder);
  }

  const url = `${baseURL}/fixed-assets/export/excel?${queryParams.toString()}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};
