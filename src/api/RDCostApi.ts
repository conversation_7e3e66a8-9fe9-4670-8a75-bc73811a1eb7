import { request } from './api';
import {
  AddRDCostParams,
  RDCostListParams,
  RDCostListResponse,
  ExportRDCostParams,
  ApiResponse,
} from '@/types/RDCost';

// 创建研发成本详情 - POST /rd-costs/details
export const createRDCostDetail = async (costData: AddRDCostParams): Promise<ApiResponse<null>> => {
  return request({
    url: '/rd-costs/details',
    method: 'POST',
    data: costData,
  });
};

// 获取研发成本详情列表 - GET /rd-costs/details
export const getRDCostsList = async (
  params: RDCostListParams,
): Promise<ApiResponse<RDCostListResponse['data']>> => {
  return request({
    url: '/rd-costs/details',
    method: 'GET',
    params: {
      ...params,
      // 确保排序参数被正确传递
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
    },
  });
};

// 获取研发成本详情 - GET /rd-costs/details/{id}
export const getRDCostDetail = async (id: string): Promise<ApiResponse<any>> => {
  return request({
    url: `/rd-costs/details/${id}`,
    method: 'GET',
  });
};

// 更新研发成本详情 - PATCH /rd-costs/details/{id}
export const updateRDCostDetail = async (
  id: string,
  costData: AddRDCostParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/rd-costs/details/${id}`,
    method: 'PATCH',
    data: costData,
  });
};

// 删除研发成本详情 - DELETE /rd-costs/details/{id}
export const deleteRDCostDetail = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/rd-costs/details/${id}`,
    method: 'DELETE',
  });
};

// 导出研发成本Excel - GET /rd-costs/export/excel
export const exportRDCostExcel = async (params: ExportRDCostParams): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  // startTime和endTime现在是必填参数
  queryParams.append('startTime', params.startTime);
  queryParams.append('endTime', params.endTime);
  if (params.detailIds && params.detailIds.length > 0) {
    // 将UUID数组转换为逗号分隔的字符串，并进行URL编码
    const detailIdsString = params.detailIds.join(',');
    queryParams.append('detailIds', encodeURIComponent(detailIdsString));
  }
  if (params.search) {
    queryParams.append('search', encodeURIComponent(params.search));
  }

  const url = `${baseURL}/rd-costs/export/excel?${queryParams.toString()}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};
