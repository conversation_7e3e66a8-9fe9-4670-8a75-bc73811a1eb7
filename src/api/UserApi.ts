import { request } from './api';
import { UserProps, UserListData, UserListParams, ApiResponse } from '@/types/user';

// 获取当前用户详细信息（profile信息）
export const getUserProfile = async (): Promise<ApiResponse<UserProps>> => {
  return request({
    url: '/auth/profile',
    method: 'GET',
  });
};

export const createUser = async (userData: {
  code: string;
  nickname: string;
  password: string;
  bankAccountName?: string;
  bankAccountNumber?: string;
  isActive: boolean;
  isCompanyAdmin: boolean;
  companyCode?: string;
  routePermissions?: {
    [key: string]: {
      read: boolean;
      create: boolean;
      delete: boolean;
      export: boolean;
      import: boolean;
      update: boolean;
    };
  };
}): Promise<ApiResponse<null>> => {
  return request({
    url: '/users',
    method: 'POST',
    data: userData,
  });
};

// 获取用户列表
export const getUserList = async (params: UserListParams): Promise<ApiResponse<UserListData>> => {
  return request({
    url: '/users',
    method: 'GET',
    params,
  });
};

// 获取用户详情
export const getUserDetail = async (userCode: string): Promise<ApiResponse<UserProps>> => {
  return request({
    url: `/users/${userCode}`,
    method: 'GET',
  });
};

export const updateUser = async (
  userCode: string,
  userData: {
    nickname: string;
    password?: string;
    bankAccountName?: string;
    bankAccountNumber?: string;
    isActive: boolean;
    routePermissions?: {
      [key: string]: {
        read: boolean;
        create: boolean;
        delete: boolean;
        export: boolean;
        import: boolean;
        update: boolean;
      };
    };
  },
): Promise<ApiResponse<null>> => {
  return request({
    url: `/users/${userCode}`,
    method: 'PATCH',
    data: userData,
  });
};

export const deleteUser = async (userCode: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/users/${userCode}`,
    method: 'DELETE',
  });
};
export const setCompanyAdmin = async ({
  userCode,
  companyCode,
}: {
  userCode: string;
  companyCode: string;
}): Promise<ApiResponse<null>> => {
  return request({
    url: `/users/${userCode}/set-company-admin`,
    method: 'POST',
    data: { companyCode },
  });
};
export const removeCompanyAdmin = async (userCode: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/users/${userCode}/remove-company-admin`,
    method: 'POST',
  });
};
