import { request } from './api';
import {
  AddOperatingAssetsParams,
  OperatingAssetsListParams,
  OperatingAssetsListResponse,
  ExportOperatingAssetsParams,
  ApiResponse,
} from '@/types/operatingAssets';

// 创建运营资产详情 - POST /operating-assets/details
export const createOperatingAssetDetail = async (
  assetData: AddOperatingAssetsParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: '/operating-assets/details',
    method: 'POST',
    data: assetData,
  });
};

// 获取运营资产详情列表 - GET /operating-assets/details
export const getOperatingAssetsList = async (
  params: OperatingAssetsListParams,
): Promise<ApiResponse<OperatingAssetsListResponse['data']>> => {
  return request({
    url: '/operating-assets/details',
    method: 'GET',
    params: {
      ...params,
      // 确保排序参数被正确传递
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
    },
  });
};

// 获取运营资产详情 - GET /operating-assets/details/{id}
export const getOperatingAssetDetail = async (id: string): Promise<ApiResponse<any>> => {
  return request({
    url: `/operating-assets/details/${id}`,
    method: 'GET',
  });
};

// 更新运营资产详情 - PATCH /operating-assets/details/{id}
export const updateOperatingAssetDetail = async (
  id: string,
  assetData: AddOperatingAssetsParams,
): Promise<ApiResponse<null>> => {
  return request({
    url: `/operating-assets/details/${id}`,
    method: 'PATCH',
    data: assetData,
  });
};

// 删除运营资产详情 - DELETE /operating-assets/details/{id}
export const deleteOperatingAssetDetail = async (id: string): Promise<ApiResponse<null>> => {
  return request({
    url: `/operating-assets/details/${id}`,
    method: 'DELETE',
  });
};

// 导出运营资产Excel - GET /operating-assets/export/excel
export const exportOperatingAssetsExcel = async (
  params: ExportOperatingAssetsParams,
): Promise<Blob> => {
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

  // 构建查询参数
  const queryParams = new URLSearchParams();
  // startTime和endTime现在是必填参数
  queryParams.append('startTime', params.startTime);
  queryParams.append('endTime', params.endTime);
  if (params.detailIds && params.detailIds.length > 0) {
    // 将UUID数组转换为逗号分隔的字符串，并进行URL编码
    const detailIdsString = params.detailIds.join(',');
    queryParams.append('detailIds', encodeURIComponent(detailIdsString));
  }
  if (params.type) {
    queryParams.append('type', params.type);
  }
  if (params.search) {
    queryParams.append('search', encodeURIComponent(params.search));
  }
  if (params.warehouseLogisticsType) {
    queryParams.append('warehouseLogisticsType', params.warehouseLogisticsType);
  }

  const url = `${baseURL}/operating-assets/export/excel?${queryParams.toString()}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  });

  if (!response.ok) {
    throw new Error('导出Excel失败');
  }

  return response.blob();
};
