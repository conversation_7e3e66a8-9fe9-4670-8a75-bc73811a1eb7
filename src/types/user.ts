export interface UserProps {
  code: string;
  nickname: string;
  bankAccountName: string | null;
  bankAccountNumber: string | null;
  routePermissions?: {
    [key: string]: {
      read: boolean;
      create: boolean;
      delete: boolean;
      export: boolean;
      import: boolean;
      update: boolean;
    };
  };
  isSuperAdmin: boolean;
  isCompanyAdmin: boolean;
  companyCode?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 用户表单数据接口
export interface UserFormData {
  code: string;
  nickname: string;
  password?: string;
  bankAccountName?: string;
  bankAccountNumber?: string;
  isCompanyAdmin: boolean;
  companyCode?: string;
  isActive: boolean;
  routePermissions?: {
    [key: string]: {
      read: boolean;
      create: boolean;
      delete: boolean;
      export: boolean;
      import: boolean;
      update: boolean;
    };
  };
}

export interface UserListData {
  users: UserProps[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 定义API响应类型
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

// 定义用户列表查询参数
export interface UserListParams {
  page: number;
  pageSize: number;
  search?: string;
  includeInactive?: boolean;
}
