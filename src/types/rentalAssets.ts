export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

export enum RentalAssetType {
  SHORT_TERM = 'short_term', // 短期租赁
  LONG_TERM = 'long_term', // 长期租赁
}

// rental-assets/details POST
export interface AddRentalAssetsParams {
  type: RentalAssetType;
  amount: number;
  screenshot: string;
  createDate: string;
  remark: string;
}
// rental-assets/details GET - 使用通用的分页参数接口
export type { RentalAssetsListParams } from './commonFinancial';

export interface Detail {
  id: string;
  rentalAssetId: string;
  type: RentalAssetType;
  amount: number;
  screenshot: string;
  remark?: string;
  createDate: string;
  typeLabel: string;
  isDeleted?: boolean;
}

export interface RentalAssetsListResponse {
  code: number;
  data: {
    details: Detail[];
    total: number;
    page: number;
    pageSize: number;
    totalAmount: number;
    systemTotalAmount: number;
  };
  message: string;
}

// /rental-assets/export/excel - 使用通用的导出参数接口
export type { ExportRentalAssetsParams } from './commonFinancial';
