// 排序字段枚举
export enum SortBy {
  CREATE_DATE = 'createDate',
  AMOUNT = 'amount',
}

// 排序方向枚举
export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

// 通用分页基础接口
export interface BaseListParams {
  page: number;
  pageSize: number;
  startTime: string;
  endTime: string;
  sortBy?: SortBy;
  sortOrder?: SortOrder;
}

// 固定资产分页参数
export interface FixedAssetsListParams extends BaseListParams {
  search?: string;
}

// 研发成本分页参数
export interface RDCostListParams extends BaseListParams {
  search?: string;
}

// 支出分页参数
export interface ExpensesListParams extends BaseListParams {
  supplierSearch?: string;
  remarkSearch?: string;
}

// 收入分页参数
export interface IncomeListParams extends BaseListParams {
  customerSearch?: string;
  responsibleUserSearch?: string;
  remarkSearch?: string;
}

// 租赁资产分页参数
export interface RentalAssetsListParams extends BaseListParams {
  type?: string; // RentalAssetType
  search?: string;
}

// 运营资产分页参数
export interface OperatingAssetsListParams extends BaseListParams {
  type?: string; // OperatingAssetType
  search?: string;
  warehouseLogisticsType?: string; // WarehouseLogisticsType - 仅对仓储物流类型有效
}

// 导出Excel时的遮罩状态管理
export interface ExportMaskState {
  isExporting: boolean;
  exportType?: 'excel' | 'template';
}

// 通用导出Excel参数基础接口
export interface BaseExportParams {
  startTime: string;
  endTime: string;
  detailIds?: string[];
  sortBy?: SortBy;
  sortOrder?: SortOrder;
}

// 固定资产导出参数
export interface ExportFixedAssetsParams extends BaseExportParams {
  search?: string;
}

// 研发成本导出参数
export interface ExportRDCostParams extends BaseExportParams {
  search?: string;
}

// 支出导出参数
export interface ExportExpenseParams extends BaseExportParams {
  supplierSearch?: string;
  remarkSearch?: string;
}

// 收入导出参数
export interface ExportIncomeParams extends BaseExportParams {
  customerSearch?: string;
  responsibleUserSearch?: string;
  remarkSearch?: string;
}

// 租赁资产导出参数
export interface ExportRentalAssetsParams extends BaseExportParams {
  type?: string; // RentalAssetType
  search?: string;
}

// 运营资产导出参数
export interface ExportOperatingAssetsParams extends BaseExportParams {
  type?: string; // OperatingAssetType
  search?: string;
  warehouseLogisticsType?: string; // WarehouseLogisticsType - 仅对仓储物流类型有效
}
