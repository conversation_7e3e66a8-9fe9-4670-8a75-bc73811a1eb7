export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message?: string;
}

export enum OperatingAssetType {
  HUMAN_RESOURCES = 'human_resources', // 人力资产
  WAREHOUSE_LOGISTICS = 'warehouse_logistics', // 仓储物流
  ADMINISTRATIVE_CONSUMPTION = 'administrative_consumption', // 行政消耗
  OTHER = 'other', // 其他（第四种类型）
}

export enum HumanResourcesAuditStatus {
  PENDING = 'pending', // 未审核
  APPROVED = 'approved', // 已审核
}

// 仓储物流收支类型枚举
export enum WarehouseLogisticsType {
  INCOME = 'income', // 收入
  EXPENSE = 'expense', // 支出
}

// operating-assets/details POST
export interface AddOperatingAssetsParams {
  type: OperatingAssetType;
  amount: number;
  screenshot: string;
  remark?: string;
  // 人力资产专用字段
  humanResourcesAuditStatus?: HumanResourcesAuditStatus;
  employeeInfo?: string;
  // 仓储物流专用字段
  warehouseLogisticsType?: WarehouseLogisticsType;
  createDate: string;
}
// operating-assets/details GET - 使用通用的分页参数接口
export type { OperatingAssetsListParams } from './commonFinancial';

export interface Detail {
  id: string;
  operatingAssetId: string;
  type: OperatingAssetType;
  amount: number;
  screenshot: string;
  remark?: string;
  createDate: string;
  typeLabel: string;
  isDeleted?: boolean;
  // 人力资产专用字段
  humanResourcesAuditStatus?: HumanResourcesAuditStatus | null;
  humanResourcesAuditStatusLabel?: string | null;
  employeeInfo?: string | null;
  // 仓储物流专用字段
  warehouseLogisticsType?: WarehouseLogisticsType | null;
  warehouseLogisticsTypeLabel?: string | null;
}

export interface OperatingAssetsListResponse {
  code: number;
  data: {
    details: Detail[];
    total: number;
    page: number;
    pageSize: number;
    totalAmount: number;
    systemTotalAmount: number;
  };
  message: string;
}

// /operating-assets/export/excel - 使用通用的导出参数接口
export type { ExportOperatingAssetsParams } from './commonFinancial';
